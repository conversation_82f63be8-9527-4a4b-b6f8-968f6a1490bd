const http = require('http');

const options = {
  hostname: 'ari.nayatel.com',
  port: 8088, // Make sure this is the correct HTTP port
  path: '/ari/api-docs/resources.json',
  method: 'GET',
  auth: 'ariuser:ariaitesting' // Basic Auth
};

console.log('Attempting to fetch ARI API definition...');

const req = http.request(options, (res) => {
  console.log(`STATUS: ${res.statusCode}`);
  console.log(`HEADERS: ${JSON.stringify(res.headers, null, 2)}`);
  
  let rawData = '';
  res.setEncoding('utf8');
  
  res.on('data', (chunk) => { 
    rawData += chunk; 
  });
  
  res.on('end', () => {
    console.log('--- BODY ---');
    try {
      // Try to parse as JSON to see if it's valid
      console.log(JSON.parse(rawData));
    } catch (e) {
      console.error('Error parsing JSON:', e.message);
      console.log('--- RAW BODY ---');
      console.log(rawData);
    }
    console.log('------------');
  });
});

req.on('error', (e) => {
  console.error(`Problem with request: ${e.message}`);
});

req.end();