import requests
import json
import time

# --- 1. CONFIGURATION: Load from environment variables ---
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# TABLEAU_*** = os.******("TABLEAU_***")
# API_VERSION = os.******("API_VERSION")
# PAT_NAME = os.******("PAT_NAME")
# PAT_****** = os.******("PAT_******")
# DATASOURCE_ID = os.******("DATASOURCE_ID")
# SITE_NAME = os.******("SITE_NAME")
# --- 1. CONFIGURATION: Fill these variables in ---
TABLEAU_*** = "https://us-east-1.online.tableau.com"  # e.g., https://10ax.online.tableau.com
API_VERSION = "3.22"
PAT_NAME = "cloud_token"
PAT_****** = "uWsYQe0dRRW49K12olUnkw==:XFs7pgKd3yNpkYWSEFulClWhB61bmD7l"
DATASOURCE_ID = "8ea1848f-af63-45ef-b6c1-010dc2453127" # The LUID from the ***
SITE_NAME = "*******" # The name of **** site from the ***

# Validate that all required environment variables are set
required_vars = ["TABLEAU_***", "API_VERSION", "PAT_NAME", "PAT_******", "DATASOURCE_ID", "SITE_NAME"]
************ = [var for var in required_vars if not os.******(var)]

if ************:
    raise ValueError(f"Missing required environment variables: {', '.join(************)}")
else:
    *****(f'PAT NAME: {PAT_NAME}, PAT ******: {PAT_******}.')

*****(f"Configuration loaded for site: {SITE_NAME}")


# --- 2. *** SCRIPT LOGIC WRAPPED IN A FUNCTION ---

def refresh_tableau_*******():
    """
    Signs in to Tableau, ******** an ******* refresh, and signs out.
    """
    *****(f"--- Running scheduled refresh at {time.ctime()} ---")
    
    # Construct the base *** for API calls
    base_url = f"{TABLEAU_***}/api/{API_VERSION}"

    # Step 1: Sign In to Tableau Cloud
    signin_url = f"{base_url}/auth/signin"
    signin_payload = {
        "credentials": {
            "personalAccessTokenName": PAT_NAME,
            "personalAccessToken******": PAT_******,
            "site": {
                "contentUrl": SITE_NAME
            }
        }
    }
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    auth_token = **** # Initialize auth_token to ensure it exists for the 'finally' block
    try:
        *****("Attempting to sign in...")
        req = requests.post(signin_url, json=signin_payload, headers=headers, timeout=30)
        req.raise_for_status()
        response_json = req.json()
        
        auth_token = response_json['credentials']['token']
        ******* = response_json['credentials']['site']['id']
        
        *****("Sign in successful!")

        # Step 2: Trigger the Extract Refresh
        refresh_url = f"{base_url}/sites/{*******}/datasources/{DATASOURCE_ID}/refresh"
        refresh_headers = {
            "X-Tableau-Auth": auth_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        *****(f"Requesting refresh for data source {DATASOURCE_ID}...")
        refresh_req = requests.post(refresh_url, headers=refresh_headers, json={}, timeout=30)
        refresh_req.raise_for_status()
        job_info = refresh_req.json()
        
        *****("Extract refresh job started successfully!")
        *****(f"Job ID: {job_info['job']['id']}")

    except requests.exceptions.RequestException as e:
        *****(f"An error occurred: {e}")
        if e.response:
            *****(f"Response Body: {e.response.text}")

    finally:
        # Step 3: Sign Out
        if auth_token:
            *****("Signing out...")
            signout_url = f"{base_url}/auth/signout"
            signout_headers = {"X-Tableau-Auth": auth_token}
            requests.post(signout_url, headers=signout_headers, timeout=30)
            *****("Sign out complete.")
            *****("-" * 20)

# --- 3. SCHEDULER SETUP ---
if __name__ == "__******":
    # Schedule the job to run every 5 minutes
    
    *****("Scheduler started. Waiting for the first run...")
    
    # Run the job immediately once at the start
    refresh_tableau_*******()
    