#!/bin/bash

# Tableau REST API Extract - Google Cloud Run Job Deployment Script
# This script deploys the Tableau extract job to Cloud Run and sets up a scheduler

set -e

# Configuration
IMAGE_NAME="tableau-extract-job"
JOB_NAME="tableau-extract-job"
SCHEDULER_NAME="tableau-extract-scheduler"
TAG=${1:-latest}
REGION="us-central1"
SCHEDULE="*/5 * * * *"  # Every 5 minutes

echo "🚀 Deploying Tableau Extract Job to Google Cloud Run..."
echo "Image: ${IMAGE_NAME}:${TAG}"
echo "Schedule: Every 5 minutes"

# Check if required tools are installed
command -v gcloud >/dev/null 2>&1 || { echo "❌ gcloud CLI is required but not installed. Aborting." >&2; exit 1; }
command -v docker >/dev/null 2>&1 || { echo "❌ Docker is required but not installed. Aborting." >&2; exit 1; }

# Get project ID
PROJECT_ID=${GCP_PROJECT_ID:-$(gcloud config get-value project)}

if [ -z "$PROJECT_ID" ]; then
    echo "❌ Error: GCP_PROJECT_ID not set and no default project configured"
    echo "Please set GCP_PROJECT_ID environment variable or run: gcloud config set project YOUR_PROJECT_ID"
    exit 1
fi

echo "📋 Using project: $PROJECT_ID"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ Error: .env file not found"
    echo "Please create .env file with your Tableau credentials"
    echo "You can copy from .env.example and fill in your values"
    exit 1
fi

echo "✅ Environment file found"

# Enable required APIs
echo "🔧 Enabling required Google Cloud APIs..."
gcloud services enable run.googleapis.com --project=$PROJECT_ID
gcloud services enable containerregistry.googleapis.com --project=$PROJECT_ID
gcloud services enable cloudscheduler.googleapis.com --project=$PROJECT_ID
gcloud services enable cloudbuild.googleapis.com --project=$PROJECT_ID

# Build and push Docker image
echo "🏗️  Building Docker image..."
docker build -t gcr.io/${PROJECT_ID}/${IMAGE_NAME}:${TAG} .

echo "📤 Pushing image to Container Registry..."
docker push gcr.io/${PROJECT_ID}/${IMAGE_NAME}:${TAG}

# Deploy Cloud Run Job
echo "🚀 Deploying Cloud Run Job..."

# Read environment variables from .env file
TABLEAU_URL=$(grep TABLEAU_URL .env | cut -d '=' -f2)
API_VERSION=$(grep API_VERSION .env | cut -d '=' -f2)
SITE_NAME=$(grep SITE_NAME .env | cut -d '=' -f2)
PAT_NAME=$(grep PAT_NAME .env | cut -d '=' -f2)
PAT_SECRET=$(grep PAT_SECRET .env | cut -d '=' -f2)
DATASOURCE_ID=$(grep DATASOURCE_ID .env | cut -d '=' -f2)

# Deploy or update the Cloud Run Job using gcloud command
# First, try to delete existing job if it exists
gcloud run jobs delete ${JOB_NAME} --region=${REGION} --project=${PROJECT_ID} --quiet 2>/dev/null || true

# Create the new job
gcloud run jobs create ${JOB_NAME} \
    --image=gcr.io/${PROJECT_ID}/${IMAGE_NAME}:${TAG} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --set-env-vars="TABLEAU_URL=${TABLEAU_URL},API_VERSION=${API_VERSION},SITE_NAME=${SITE_NAME},PAT_NAME=${PAT_NAME},PAT_SECRET=${PAT_SECRET},DATASOURCE_ID=${DATASOURCE_ID}" \
    --memory=512Mi \
    --cpu=1 \
    --task-timeout=300 \
    --max-retries=3 \
    --parallelism=1

echo "✅ Cloud Run Job deployed successfully"

# Create or update Cloud Scheduler job
echo "⏰ Setting up Cloud Scheduler..."

# Check if scheduler job already exists
if gcloud scheduler jobs describe ${SCHEDULER_NAME} --location=${REGION} --project=${PROJECT_ID} >/dev/null 2>&1; then
    echo "📝 Updating existing scheduler job..."
    gcloud scheduler jobs update http ${SCHEDULER_NAME} \
        --location=${REGION} \
        --schedule="${SCHEDULE}" \
        --uri="https://${REGION}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${PROJECT_ID}/jobs/${JOB_NAME}:run" \
        --http-method=POST \
        --oauth-service-account-email="${PROJECT_ID}@appspot.gserviceaccount.com" \
        --oauth-token-scope="https://www.googleapis.com/auth/cloud-platform" \
        --project=${PROJECT_ID}
else
    echo "🆕 Creating new scheduler job..."
    gcloud scheduler jobs create http ${SCHEDULER_NAME} \
        --location=${REGION} \
        --schedule="${SCHEDULE}" \
        --uri="https://${REGION}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${PROJECT_ID}/jobs/${JOB_NAME}:run" \
        --http-method=POST \
        --oauth-service-account-email="${PROJECT_ID}@appspot.gserviceaccount.com" \
        --oauth-token-scope="https://www.googleapis.com/auth/cloud-platform" \
        --project=${PROJECT_ID}
fi

echo "✅ Cloud Scheduler configured successfully"

# Test the job manually
echo "🧪 Testing the job manually..."
gcloud run jobs execute ${JOB_NAME} --region=${REGION} --project=${PROJECT_ID} --wait

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Summary:"
echo "  - Job Name: ${JOB_NAME}"
echo "  - Scheduler: ${SCHEDULER_NAME}"
echo "  - Schedule: Every 5 minutes (${SCHEDULE})"
echo "  - Region: ${REGION}"
echo "  - Project: ${PROJECT_ID}"
echo ""
echo "🔍 Useful commands:"
echo "  - View job logs: gcloud logging read 'resource.type=cloud_run_job AND resource.labels.job_name=${JOB_NAME}' --limit=50 --format='table(timestamp,textPayload)'"
echo "  - Manual execution: gcloud run jobs execute ${JOB_NAME} --region=${REGION}"
echo "  - View scheduler: gcloud scheduler jobs describe ${SCHEDULER_NAME} --location=${REGION}"
echo "  - Pause scheduler: gcloud scheduler jobs pause ${SCHEDULER_NAME} --location=${REGION}"
echo "  - Resume scheduler: gcloud scheduler jobs resume ${SCHEDULER_NAME} --location=${REGION}"
