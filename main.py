import requests, aiohttp, time
import ujson as json
import asyncio
import websockets
from fastapi import FastAPI, WebSocket, Request, Depends
from fastapi.responses import HTMLResponse
from fastapi.websockets import WebSocketDisconnect
from twilio.twiml.voice_response import VoiceResponse, Connect, Stream, Parameter, Say
from twilio.rest import Client
from dotenv import load_dotenv
from urllib.parse import parse_qs
import asyncpg
from datetime import datetime
import math
import logging
import base64
import os       

from db import db_manager
from DBEngine import lifespan, get_pool, get_pool_ws
from decrypt import decrypt_aes_256_cbc
from prompts import get_voice_instruct

load_dotenv()
db = db_manager()

# Configuration
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
PORT = int(os.getenv('PORT', 5050))
ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
DBWRAPPER_URL = os.getenv('DBWRAPPER_URL')
MODEL = os.getenv('MODEL')
VOICE = os.getenv('VOICE')
RAG_URL = os.getenv('RAG_URL')
LOG_EVENT_TYPES = ['error']
# LOG_EVENT_TYPES = ['error','response.cancelled','response.done',
#                    'response.created','conversation.item.created',
#                    'conversation.item.deleted'
#                    ]
SHOW_TIMING_MATH = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


# --- FastAPI Application Instance ---
app = FastAPI(lifespan=lifespan)

if not OPENAI_API_KEY:
    raise ValueError('Missing the OpenAI API key. Please set it in the .env file.')


@app.get("/")
async def index_page():
    return {"message": "Twilio Media Stream Server is running!"}

@app.api_route("/incoming-call", methods=["GET", "POST"])
async def handle_incoming_call(request: Request,
                                pool: asyncpg.Pool = Depends(get_pool)):
    """Handle incoming call and return TwiML response to connect to Media Stream."""
    response = VoiceResponse()
    response.say("Please wait while we connect your call to the AI voice assistant, powered by Najoomie Technologies")
    # response.play('https://najoomi-aws-storage.s3.us-east-1.amazonaws.com/testing/Kalimba-%5BAudioTrimmer.com%5D+(1).mp3', loop=1)

    caller = request.query_params.get("Called", "unknown")
    encrypted_data = request.query_params.get("data","N/A")

    try:
        org_id = await decrypt_aes_256_cbc(encrypted_data)
        org_id = org_id.split('=')[-1]

        secret = await db.validate_secret(pool, org_id)
        if secret and secret == encrypted_data:
            print(f'Organisation: {org_id}, Status: Valid')
        else:
            logger.warning(f"Failed to validate incoming call: Secret mismatch for org_id {org_id}")
            response = VoiceResponse()
            response.say("Sorry, your call could not be authenticated.")
            response.hangup()
            return HTMLResponse(content=str(response), media_type="application/xml")

    except Exception as e:
        logger.warning(f"Failed decrypting client secret: {e}")
        response = VoiceResponse()
        response.say("Sorry, there was an issue with your call. Please contact customer support.")
        response.hangup()
        return HTMLResponse(content=str(response), media_type="application/xml")

    host = request.url.hostname
    # Create the connect and stream objects
    connect = Connect()
    stream = Stream(url=f'wss://{host}/media-stream')
    stream.parameter(name="CallerNumber", value=org_id)

    connect.append(stream)
    response.append(connect)
    return HTMLResponse(content=str(response), media_type="application/xml")


@app.websocket("/media-stream")
async def handle_media_stream(
    websocket: WebSocket,
    pool: asyncpg.Pool = Depends(get_pool_ws) # Inject the pool here
    ):
    """Handle WebSocket connections between Twilio and OpenAI."""
    await websocket.accept()
    print("Client connected")
    # Connection specific state
    stream_sid = None
    latest_media_timestamp = 0
    last_assistant_item = None
    mark_queue = []
    response_start_timestamp_twilio = None
    dtmfs = None  # Global variable to store the latest DTMF input
    current_response_cumulative_duration_ms = 0


    async for message in websocket.iter_text():
        data = json.loads(message)
        if data.get("event") == 'start':
            stream_sid = data['start']['streamSid']
            call_sid   = data["start"]["callSid"]
            org_id = data.get("start", {}).get("customParameters", {}).get("CallerNumber", "unknown")
            break

    client = Client(ACCOUNT_SID, AUTH_TOKEN)

    client.calls(call_sid) \
        .recordings \
        .create(
            recording_channels="dual",
            recording_status_callback=f"{DBWRAPPER_URL}/inbound_status",
            recording_status_callback_event=["completed"],
            recording_status_callback_method='GET'
        )
    print(f"Started recording CallSid={call_sid}")
    function_tools, system_message, api_links  = await db.read_function_schema(pool, client_id=org_id)

    # Check if database function failed
    if function_tools is None or system_message is None or api_links is None:
        logger.error(f"Failed to load function schema for org_id: {org_id}.")
        await websocket.close(code=1011, reason="Database configuration error")
        return
    
    additional_prompt = get_voice_instruct('urdu')
    system_message = system_message + '\n' + additional_prompt

    logger.info(f'New Prompt: {system_message}')

    async with websockets.connect(
        f'wss://api.openai.com/v1/realtime?model={MODEL}',
        extra_headers={
            "Authorization": f"Bearer {OPENAI_API_KEY}",
            "OpenAI-Beta": "realtime=v1"
        }
    ) as openai_ws:
        print(f'Connected to Speech to Speech model: {MODEL}')
        await initialize_session(openai_ws,function_tools,system_message)


        async def receive_from_twilio():
            dtmf_list = []
              # Access the global variable
            """Receive audio data from Twilio and send it to the OpenAI Realtime API."""
            nonlocal stream_sid, latest_media_timestamp, dtmfs
            try:
                async for message in websocket.iter_text():
                    data = json.loads(message)
                    if data['event'] == 'media' and openai_ws.open:
                        latest_media_timestamp = int(data['media']['timestamp'])
                        audio_append = {
                            "type": "input_audio_buffer.append",
                            "audio": data['media']['payload']
                        }
                        await openai_ws.send(json.dumps(audio_append))
                    elif data['event'] == 'start':
                        stream_sid = data['start']['streamSid']
                        print(f"Incoming stream has started {stream_sid}")
                        response_start_timestamp_twilio = None
                        latest_media_timestamp = 0
                        last_assistant_item = None
                    elif data['event'] == 'mark':
                        if mark_queue:
                            mark_queue.pop(0)
                    elif data['event'] == 'dtmf' and openai_ws.open:
                        dtmf = data['dtmf']['digit']
                        if dtmf == '#':
                            dtmfs = ''.join(dtmf_list)
                            print(f'DTMF dialed: {dtmfs}')
                            dtmf_input = dtmfs
                            await send_dtmf_arguments(openai_ws, dtmfs)  # Send a message to OpenAI to let it know DTMF input is ready
                            dtmf_list.clear()
                        else:
                            dtmf_list.append(dtmf)

            except WebSocketDisconnect:
                print("Client disconnected.")
                if openai_ws.open:
                    await openai_ws.close()


        async def send_to_twilio():
            """Receive events from the OpenAI Realtime API, send audio back to Twilio."""
            nonlocal stream_sid, last_assistant_item, response_start_timestamp_twilio, dtmfs, current_response_cumulative_duration_ms
            try:
                async for openai_message in openai_ws:
                    response = json.loads(openai_message)
                    if response['type'] in LOG_EVENT_TYPES:
                        print(f"Received event: {response['type']}", response)

                    if response.get('type') == 'response.audio.delta' and 'delta' in response:
                        # Check if this is the start of a new item compared to the last one processed
                        new_item_id = response.get('item_id')
                        if new_item_id and new_item_id != last_assistant_item:
                            # Reset duration for the new item
                            current_response_cumulative_duration_ms = 0
                            last_assistant_item = new_item_id # Update last_assistant_item here
                            response_start_timestamp_twilio = None # Reset timestamp for the new response

                        # --- Send audio to Twilio ---
                        # print('sending audio to twilio....')
                        audio_payload = response['delta'] # Re-encode needed? Check if already base64
                        audio_delta = {
                            "event": "media",
                            "streamSid": stream_sid,
                            "media": {
                                "payload": audio_payload # Use the re-encoded payload
                            }
                        }
                        await websocket.send_json(audio_delta)
                        # print('audio succesffuly sent..')
                        # Calculate duration of this delta
                        decoded_audio = base64.b64decode(response['delta'])
                        # Assuming 8kHz sample rate, 16-bit PCM (2 bytes/sample) or 8-bit mu-law (1 byte/sample)
                        # Twilio typically uses 8kHz mu-law (8000 bytes per second or 8 bytes per ms)
                        # Check OpenAI Realtime API docs for exact format if unsure. Let's assume 8kHz mu-law.
                        bytes_per_millisecond = 8
                        chunk_duration_ms = len(decoded_audio) / bytes_per_millisecond
                        current_response_cumulative_duration_ms += chunk_duration_ms
                        # --- Handle timestamp for latency calculation (if still needed elsewhere) ---
                        if response_start_timestamp_twilio is None:
                            # This timestamp is less critical now for truncation but might be useful for other latency metrics
                            response_start_timestamp_twilio = latest_media_timestamp
                            # if SHOW_TIMING_MATH:
                            #     print(f"Setting start timestamp for new response: {response_start_timestamp_twilio}ms")

                        # --- Send mark ---
                        await send_mark(websocket, stream_sid)

                    # Handle speech started events
                    if response.get('type') == 'input_audio_buffer.speech_started':
                        if last_assistant_item:
                            await handle_speech_started_event() # Pass the tracked duration

                    # Handling function calls
                    if response.get('type') == 'response.done':
                        for item in response['response']['output']:
                            if item['type'] == 'function_call':
                                arguments = json.loads(item['arguments'])
                                call_id = item["call_id"]
                                function_name = item['name']

                                result = None # Initialize result

                                logger.info(f"Function invoked: {function_name}, Arguments: {arguments}")
    
                                if function_name == 'handle_outof_scope_cases':
                                    # Handles RAG calls
                                    headers = {"Content-Type": "application/json"}
                                    payload = {"question": arguments.get('user_query'),"history": [],"organisation_id": org_id,"newPrompt": "","lead_gen": False,"voicemessage": False,"user_language": "en","provideindexname": "","providenamespace": "","agent_module_subscription": False,"user_data": [],"user_language_voice": None,"inboundcall": True}
                                    
                                    logger.info(f"RAG URL: {RAG_URL}")
                                    logger.info(f"Payload: {payload}")
                                    async with aiohttp.ClientSession() as session:
                                        try:
                                            async with session.post(RAG_URL, json=payload, headers=headers, timeout=aiohttp.ClientTimeout(total=7.0)) as response:
                                                response.raise_for_status() # Raise for 4xx/5xx
                                                result_json = await response.json()
                                                result = result_json['text']
                                                logger.info(f"Response: {result}")
                                        except aiohttp.ClientError as exc:
                                            logger.error(f"HTTP request failed for {function_name} to {RAG_URL}: {exc}")
                                    

                                elif function_name == 'get_order_status' and org_id == 'TCS':
                                    result = await get_order_status_async(arguments, pool)

                                elif function_name == 'register_complaint' and org_id == 'TCS':
                                    result = await store_customer_complaint(arguments, pool)

                                elif function_name == 'complaint_status' and org_id == 'TCS':
                                    result = await complaint_status(arguments, pool)

                                else:

                                    if len(api_links) == 1:
                                        api_link, method = next(iter(api_links.values()))
                                    else:
                                        api_link, method = api_links[function_name][0], api_links[function_name][1]


                                    logger.info(f"External function invoked: {function_name}, Method: {method}")
                                    headers = {"Content-Type": "application/json"}
                                    logger.info(f"Request URL: {api_link}, Arguments: {arguments}")
                                    
                                    async with aiohttp.ClientSession() as session:
                                        try:

                                            if method == 'POST':
                                                async with session.post(api_link, json=arguments, headers=headers, timeout=aiohttp.ClientTimeout(total=5.0)) as response:
                                                    response.raise_for_status() # Raise for 4xx/5xx
                                                    result = await response.json()
                                                    logger.info(f"Response: {result}")
                                            elif method == 'GET':
                                                async with session.get(api_link, params=arguments, headers=headers, timeout=aiohttp.ClientTimeout(total=5.0)) as response:
                                                    response.raise_for_status() # Raise for 4xx/5xx
                                                    result = await response.json()
                                                    logger.info(f"Response: {result}")
                                            elif method == 'PUT':
                                                async with session.put(api_link, json=arguments, headers=headers, timeout=aiohttp.ClientTimeout(total=5.0)) as response:
                                                    response.raise_for_status() # Raise for 4xx/5xx
                                                    result = await response.json()
                                                    logger.info(f"Response: {result}")
                                            
                                        except aiohttp.ClientError as exc:
                                            logger.error(f"HTTP request failed for {function_name} to {api_link}: {exc}")


                                if dtmfs:
                                    delete_dtmf_message = {
                                        "type": "conversation.item.delete",  #This should remove any dtmfs in the current context
                                        "item_id": "msg_001"
                                    }

                                    await openai_ws.send(json.dumps(delete_dtmf_message))
                                    dtmfs = None
                                    print('Deleted dtmf in conversation context')


                                function_output = json.dumps(result)
                                # logger.info(f"Function output: {function_output}")
                                response_message = {
                                    "type": "conversation.item.create",
                                    "item": {
                                        "type": "function_call_output",
                                        "output": function_output,
                                        "call_id": call_id
                                    }
                                }
                                await openai_ws.send(json.dumps(response_message))
                                await openai_ws.send(json.dumps({"type": "response.create"}))


            except WebSocketDisconnect:
                    logger.info('Error: Websocket Disconnected')
            except Exception as e:
                logger.error(f"Error in send_to_twilio: {e}")

        async def handle_speech_started_event():
            """Handle interruption when the caller's speech starts."""
            nonlocal response_start_timestamp_twilio, last_assistant_item
            nonlocal current_response_cumulative_duration_ms, stream_sid, mark_queue, websocket, openai_ws # Ensure all needed nonlocals are listed

            if mark_queue and last_assistant_item: # Check mark_queue and item_id are valid
                # Use the tracked cumulative duration directly, flooring it to be safe.
                truncation_point_ms = math.floor(current_response_cumulative_duration_ms)

                # Ensure truncation point is not negative (shouldn't happen with cumulative, but safe)
                truncation_point_ms = max(0, truncation_point_ms)

                print(f"Interrupting item {last_assistant_item} at tracked duration {current_response_cumulative_duration_ms:.2f}ms (sending truncate at {truncation_point_ms}ms)")

                truncate_event = {
                    "type": "conversation.item.truncate",
                    "item_id": last_assistant_item,
                    "content_index": 0, # Assuming single content item (audio)
                    "audio_end_ms": truncation_point_ms # Use the floored calculated point without buffer
                }
                try:
                    await openai_ws.send(json.dumps(truncate_event))
                    print(f"Sent truncate event for item {last_assistant_item} at {truncation_point_ms}ms")
                except Exception as e:
                    print(f"Error sending truncate event: {e}")
                    # Decide if you should still clear Twilio queue etc. even if truncate fails

                # Clear Twilio's playback buffer
                print(f"Sending clear event to Twilio for stream {stream_sid}")
                await websocket.send_json({
                    "event": "clear",
                    "streamSid": stream_sid
                })

                # Reset state after handling interruption
                print("Resetting state after interruption.")
                mark_queue.clear()
                last_assistant_item = None
                response_start_timestamp_twilio = None # If you still use this elsewhere
                current_response_cumulative_duration_ms = 0 # Reset the duration tracker

            # else:
                # print("Speech started detected, but no active response item or mark queue to interrupt.")

        async def send_mark(connection, stream_sid):
            if stream_sid:
                mark_event = {
                    "event": "mark",
                    "streamSid": stream_sid,
                    "mark": {"name": "responsePart"}
                }
                await connection.send_json(mark_event)
                mark_queue.append('responsePart')

        await asyncio.gather(receive_from_twilio(), send_to_twilio())

async def send_dtmf_arguments(openai_ws, dtmfs):
    """Send Dial Pad Input to the model"""
    dtmf_input_json = {
        "type": "conversation.item.create",
        "item": {
            "id":"msg_001",
            "type": "message",
            "role": "system",
            "content": [
                {
                    "type": "input_text",
                    "text": (
                       f"The user entered: {dtmfs} via keypad. Call the relevant function and use these as function call arguments."
                    )
                }
            ]
        }
    }

    await openai_ws.send(json.dumps(dtmf_input_json))

async def send_initial_conversation_item(openai_ws):
    """Send initial conversation item if AI talks first."""
    initial_conversation_item = {
        "type": "conversation.item.create",
        "item": {
            "type": "message",
            "role": "user",
            "content": [
                {
                    "type": "input_text",
                    "text": (
                       "Greet the user in Urdu with 'As-salamu alaykum! I am an AI voice assistant powered by Najoomie Technologies."
                        "How may I help you?"
                        "Do not use 'Wa alaykum as-salam as a greeting."
                    )
                }
            ]
        }
    }
    await openai_ws.send(json.dumps(initial_conversation_item))
    await openai_ws.send(json.dumps({"type": "response.create"}))


async def handle_outof_scope_cases(openai_ws,arguments,call_id):
    pass

async def initialize_session(openai_ws,function_tools,system_message):
    """Control initial session with OpenAI."""
    session_update = {
        "type": "session.update",
        "session": {
            "turn_detection": {
                "type": "server_vad",
                "threshold": 0.6,
                "prefix_padding_ms": 300,
                "silence_duration_ms": 400,
                "create_response": True,
                },
            "input_audio_noise_reduction": {"type":"near_field"},
            "input_audio_format": "g711_ulaw",
            "output_audio_format": "g711_ulaw",
            "voice": VOICE,
            "instructions": system_message,
            "speed": 1.2,
            "modalities": ["text", "audio"],
            "temperature": 0.8,
            "tools": function_tools,
            "tool_choice": "auto"

        }}

    # print('Sending session update:', json.dumps(session_update))
    await openai_ws.send(json.dumps(session_update))
    # Uncomment the next line to have the AI speak first
    await send_initial_conversation_item(openai_ws)

async def get_order_status_async(data, pool):
    tracking_number = data.get('tracking_number')
    try:
        async with pool.acquire() as conn:
            order_details=await conn.fetchrow(
                "SELECT * FROM chatbot.order_status_data WHERE tracking_number = $1",
                data['tracking_number']
            )
            key = order_details[1]
            received_by = order_details[2]
            delivery_date = order_details[3]
            booking_date = order_details[4]
            origin = order_details[5]
            destination = order_details[6]

            mappings = {
                "SR":"SHORT RECEIVE",
                "TH": "Shipment is temporary on hold at TCS facility","HC": "FRI/SAT CLOSED","FGO": "Forwarded to Final Dest.",
                "AB": "Abandon Shipments",
                "BA": "BAD ADDRESS","CA": "CLOSE ON ARRIVAL","CD": "Clearence delay",
                "CN": "CONS NOT AVAIL","CS": "CONSIGNEE SHIFT","DH": "Dead Hold","DM": "DAMAGED",
                "GT":"GIVEN TO","HO":"HANDED OVER TO","HWC":"Hold with Customs","MR":"M/R RCVD & FWDD TO DEST",
                "NP": "NO SUCH CONSIGN","NS": "NON SERVICE AREA","OH":"ON HOLD","POK":"Partial Piece Delivered",
                "RA":"RETURN TO AREA","RD":"REFUSED DELIVERY","RDV":"Vendor refused to accept","SC":"HOLD FOR COLLECTION",
                "SD":"Scheduled for the next delivery cycle","SR":"SHORT RECEIVE",
                "ST":"STOP DELIVERY","UL":"Unable To Contact","VRD":"Vendor Refused",
                "WR":"WRONG ENTRY","99":"CORRECTED DTL"
            }

            order_status = mappings.get(key)
            order_info = {"tracking_number":tracking_number}

            if order_status:
                order_info['order_status'] = order_status
                order_info['received_by'] = received_by
                order_info['delivery_date'] = str(delivery_date)
                order_info['booking_date'] = str(booking_date)
                order_info['origin'] = origin
                order_info['destination'] = destination
                # print(order_info)
                return order_info
            else:
                print('No order status found')
                # order_info['order_status']='No record found'
                return {"error": f"Order with tracking number {tracking_number} not found."}
    except Exception as e:
        return {"error": f"Order error"}



# Define the async function, accepting the pool as an argument
async def store_customer_complaint(data: dict, pool: asyncpg.Pool) -> dict:
    """
    Stores a customer complaint if it doesn't already exist for the tracking number.
    Uses asyncpg via a passed-in connection pool.
    Returns a dictionary with status and potentially complaint_number or error message.
    """
    tracking_number = data.get('tracking_number')
    complaint_text = data.get('complaint_description')
    complaint_category = data.get('complaint_category')


    try:
        # Acquire a connection from the pool
        async with pool.acquire() as conn:
            # Use a transaction to ensure atomicity (check then insert)
            async with conn.transaction():
                # --- Check if complaint for this tracking number already exists ---
                # Use fetchval to get the complaint_number directly if it exists, or None otherwise
                check_query = """
                    SELECT complaint_number
                    FROM chatbot.complaint_data
                    WHERE tracking_number = $1;
                    """
                existing_complaint_number = await conn.fetchval(check_query, tracking_number)

                if existing_complaint_number is not None:
                    # Complaint already exists
                    message = f"This complaint has already been registered under the ID: {existing_complaint_number}"
                    logger.info(f"Complaint for tracking {tracking_number} already registered (ID: {existing_complaint_number})")
                    return {"status": "already_registered", "message": message, "complaint_number": existing_complaint_number}
                else:
                    # --- Complaint does not exist, proceed with insertion ---
                    complaint_status = 'pending update'
                    # Use database's NOW() for timestamp consistency
                    # Use RETURNING to get the newly generated complaint_number (assuming it's SERIAL/IDENTITY type)
                    insert_query = """
                    INSERT INTO chatbot.complaint_data
                        (tracking_number, complaint_description, complaint_category, complaint_date, complaint_status)
                    VALUES ($1, $2, $3, NOW(), $4)
                    RETURNING complaint_number;
                    """
                    # Use fetchval again to get the single returned value (the new ID)
                    new_complaint_number = await conn.fetchval(
                        insert_query,
                        tracking_number,
                        complaint_text,
                        complaint_category,
                        complaint_status
                    )

                    if new_complaint_number is not None:
                        # Insertion successful, transaction will commit automatically
                        message = f"Complaint registered successfully! For future queries here is your complaint number: {new_complaint_number}"
                        logger.info(f"Successfully registered complaint for tracking {tracking_number}, new ID: {new_complaint_number}")
                        return {"status": "registered", "message": message, "complaint_number": new_complaint_number}
                    else:
                        # This case should be rare if INSERT works and RETURNING is set up
                        logger.error(f"Complaint insertion for {tracking_number} seemed successful but did not return a complaint number.")
                        return {"error": "Failed to register complaint and retrieve ID."}

    except asyncpg.PostgresError as db_err:
        # Log specific database errors
        logger.error(f"Database error storing complaint for tracking {tracking_number}: {db_err}")
        # Return a generic error message
        return {"error": "Database error during complaint registration."}
    except Exception as e:
        # Log unexpected errors, including traceback
        logger.exception(f"Unexpected error storing complaint for tracking {tracking_number}: {e}")
        return {"error": "An unexpected server error occurred during complaint registration."}

async def complaint_status(data: dict, pool: asyncpg.Pool) -> dict: # Return dict
    """Retrieves complaint status. Returns a dict with 'status' or 'error'."""
    complaint_number_str = data.get('complaint_number')
    # --- Input Validation ---
    if not complaint_number_str:
        logger.warning("Missing complaint_number in request data.")
        return {"error": "Missing required field: complaint_number"} # Return error dict
    try:
        complaint_number = int(complaint_number_str)
    except (ValueError, TypeError):
        logger.warning(f"Invalid complaint_number format: {complaint_number_str}")
        return {"error": "Invalid complaint_number format. Must be a number."} # Return error dict

    try:
        async with pool.acquire() as conn:
            query = "SELECT complaint_status FROM chatbot.complaint_data WHERE complaint_number = $1;"
            status = await conn.fetchval(query, complaint_number)

            if status is not None:
                logger.info(f"Status for complaint {complaint_number}: {status}")
                # Return success dict
                return {"status": f"Here is the latest update to your complaint ({complaint_number}): {status}"}
            else:
                logger.warning(f"Complaint number {complaint_number} not found.")
                # Return specific error dict for not found
                return {"error": f"Complaint with number {complaint_number} not found."}

    except asyncpg.PostgresError as db_err:
        logger.error(f"DB error for complaint {complaint_number}: {db_err}")
        # Return generic DB error dict
        return {"error": "Database error retrieving complaint status."}
    except Exception as e:
        logger.exception(f"Unexpected error for complaint {complaint_number}: {e}")
        # Return generic unexpected error dict
        return {"error": "An unexpected server error occurred."}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)
    # db.read_function_schema(client_id='TCS')


